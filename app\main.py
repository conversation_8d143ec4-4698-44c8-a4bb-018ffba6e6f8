"""
FastAPI主应用
"""

import time
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import J<PERSON>NResponse, StreamingResponse
from app.config.settings import settings
from app.utils.logger import get_logger

# 获取日志记录器
logger = get_logger(__name__)
from app.services.config_generation import config_generation_service
from app.models.request import ConfigGenerationRequest
from app.models.response import (
    ConfigGenerationResponse, HealthCheckResponse, ErrorResponse
)
from app.api.config_recommend import router as config_recommend_router
from app.utils.client_disconnect_monitor import create_disconnect_monitor


# 应用启动时间
app_start_time = time.time()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("数通设备配置生成智能体服务启动中...")
    logger.info(f"应用版本: {settings.app_version}")
    logger.info(f"调试模式: {settings.debug}")
    
    yield
    
    # 关闭时执行
    logger.info("数通设备配置生成智能体服务关闭中...")


# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="数通设备配置生成智能体服务",
    lifespan=lifespan,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=settings.cors_methods,
    allow_headers=settings.cors_headers,
)

# 添加Gzip压缩中间件
app.add_middleware(GZipMiddleware, minimum_size=1000)

# 注册路由
app.include_router(config_recommend_router, prefix="", tags=["配置推荐"])


# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录请求日志"""
    start_time = time.time()
    
    # 记录请求信息
    logger.info(
        f"请求开始: {request.method} {request.url.path} "
        f"客户端: {request.client.host if request.client else 'unknown'}"
    )
    
    try:
        response = await call_next(request)
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 记录响应信息
        logger.info(
            f"请求完成: {request.method} {request.url.path} "
            f"状态码: {response.status_code} "
            f"处理时间: {process_time:.3f}s"
        )
        
        # 添加处理时间到响应头
        response.headers["X-Process-Time"] = str(process_time)
        
        return response
        
    except Exception as e:
        process_time = time.time() - start_time
        logger.error(
            f"请求异常: {request.method} {request.url.path} "
            f"错误: {str(e)} "
            f"处理时间: {process_time:.3f}s"
        )
        raise


# 全局异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    logger.error(f"HTTP异常: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            success=False,
            message=exc.detail,
            code=exc.status_code,
            error_type="HTTPException"
        ).dict()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"未处理异常: {type(exc).__name__} - {str(exc)}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            success=False,
            message="服务器内部错误",
            code=500,
            error_type=type(exc).__name__,
            error_details={"message": str(exc)}
        ).dict()
    )


# 健康检查接口
@app.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """健康检查"""
    uptime = time.time() - app_start_time
    
    return HealthCheckResponse(
        success=True,
        message="服务正常",
        code=200,
        version=settings.app_version,
        uptime=uptime,
        status="healthy"
    )


# 配置生成接口
@app.post("/generate-config")
async def generate_config(request: ConfigGenerationRequest, http_request: Request):
    """生成配置接口"""
    try:
        logger.info(f"收到配置生成请求: {request.dict()}")

        # 创建取消令牌
        cancellation_token = asyncio.Event()

        # 创建客户端断开连接监控器
        disconnect_monitor = create_disconnect_monitor(
            http_request=http_request,
            cancellation_token=cancellation_token,
            is_streaming=request.stream
        )

        # 启动监控任务（流式和非流式都需要）
        disconnect_monitor.start_monitoring()

        try:
            if request.stream:
                print('流式返回')
                # 流式返回
                async def generate_stream():
                    async for chunk in config_generation_service.generate_config_stream(request, cancellation_token):
                        # 检查是否被取消
                        if cancellation_token.is_set():
                            logger.info("流式响应被取消")
                            break
                        yield f"data: {chunk}\n\n"
                    yield "data: [DONE]\n\n"

                return StreamingResponse(
                    generate_stream(),
                    media_type="text/event-stream",
                    headers={
                        "Cache-Control": "no-cache",
                        "Connection": "keep-alive",
                        "Access-Control-Allow-Origin": "*",
                        "Access-Control-Allow-Headers": "*",
                    }
                )
            else:
                # 非流式返回
                result = await config_generation_service.generate_config(request, cancellation_token)
                return result
        finally:
            # 清理监控任务
            await disconnect_monitor.stop_monitoring()

    except asyncio.CancelledError:
        logger.info("配置生成请求被取消")
        raise HTTPException(status_code=499, detail="请求被客户端取消")
    except ValueError as e:
        logger.error(f"请求参数错误: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"配置生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail="配置生成失败")


# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "数通设备配置生成智能体服务",
        "version": settings.app_version,
        "status": "running"
    }


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        workers=1 if settings.debug else settings.workers
    )
