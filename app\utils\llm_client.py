"""
LLM客户端工具
"""

import json
import asyncio
from typing import Dict, Any, Optional, AsyncGenerator
import httpx
from loguru import logger
from tenacity import retry, stop_after_attempt, wait_exponential

from app.config.settings import settings


class LLMClient:
    """LLM客户端类"""
    
    def __init__(self):
        self.base_url = settings.llm_base_url
        self.api_key = settings.llm_api_key
        self.model = settings.llm_model
        self.timeout = settings.llm_timeout
        self.max_retries = settings.llm_max_retries
        
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def _make_request(
        self,
        payload: Dict[str, Any],
        stream: bool = False,
        cancellation_token: Optional[asyncio.Event] = None
    ) -> httpx.Response:
        """发起HTTP请求"""
        try:
            logger.info(f"尝试连接到: {self.base_url}")
            logger.info(f"超时设置: {self.timeout}秒")

            async with httpx.AsyncClient(
                timeout=httpx.Timeout(self.timeout, connect=10.0),
                verify=False  # 暂时禁用SSL验证以排除SSL问题
            ) as client:
                logger.info("开始发送HTTP请求...")

                # 如果有取消令牌，创建可取消的任务
                if cancellation_token:
                    request_task = asyncio.create_task(
                        client.post(
                            self.base_url,
                            headers=self.headers,
                            json=payload,
                            timeout=self.timeout
                        )
                    )

                    # 等待请求完成或取消
                    done, pending = await asyncio.wait(
                        [request_task, asyncio.create_task(cancellation_token.wait())],
                        return_when=asyncio.FIRST_COMPLETED
                    )

                    # 如果取消令牌被设置，取消请求
                    if cancellation_token.is_set():
                        request_task.cancel()
                        for task in pending:
                            task.cancel()
                        raise asyncio.CancelledError("请求被用户取消")

                    # 获取请求结果
                    response = request_task.result()
                else:
                    response = await client.post(
                        self.base_url,
                        headers=self.headers,
                        json=payload,
                        timeout=self.timeout
                    )

                logger.info(f"收到响应，状态码: {response.status_code}")
                response.raise_for_status()
                return response

        except httpx.ConnectError as e:
            logger.error(f"连接错误: {str(e)}")
            logger.error(f"目标URL: {self.base_url}")
            raise
        except httpx.TimeoutException as e:
            logger.error(f"请求超时: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"请求异常: {type(e).__name__} - {str(e)}")
            raise
    
    async def chat_completion(
        self,
        messages: list[Dict[str, str]],
        stream: bool = False,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        cancellation_token: Optional[asyncio.Event] = None
    ) -> Dict[str, Any]:
        """聊天完成接口（非流式）"""
        payload = {
            "model": self.model,
            "messages": messages,
            "stream": stream,
            "temperature": temperature
        }
        
        if max_tokens:
            payload["max_tokens"] = max_tokens
        
        try:
            logger.info(f"发送LLM请求到: {self.base_url}")
            logger.info(f"请求头: {self.headers}")
            logger.info(f"请求体: {json.dumps(payload, ensure_ascii=False)}")

            response = await self._make_request(payload, stream=stream, cancellation_token=cancellation_token)
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {response.headers}")

            result = response.json()
            print(f"响应内容: {result}")
            logger.info(f"LLM响应: {json.dumps(result, ensure_ascii=False)}")
            return result
            
        except httpx.HTTPStatusError as e:
            logger.error(f"LLM请求HTTP错误: {e.response.status_code} - {e.response.text}")
            raise
        except httpx.RequestError as e:
            logger.error(f"LLM请求网络错误: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"LLM请求未知错误: {str(e)}")
            raise
    
    async def chat_completion_stream(
        self,
        messages: list[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        cancellation_token: Optional[asyncio.Event] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """聊天完成接口（流式）"""
        payload = {
            "model": self.model,
            "messages": messages,
            "stream": True,
            "temperature": temperature
        }
        
        if max_tokens:
            payload["max_tokens"] = max_tokens
        
        try:
            logger.info(f"发送LLM流式请求: {json.dumps(payload, ensure_ascii=False)}")
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                async with client.stream(
                    "POST",
                    self.base_url,
                    headers=self.headers,
                    json=payload
                ) as response:
                    response.raise_for_status()
                    
                    async for line in response.aiter_lines():
                        # 检查是否被取消
                        if cancellation_token and cancellation_token.is_set():
                            logger.info("流式请求被用户取消")
                            raise asyncio.CancelledError("流式请求被用户取消")

                        # logger.info(line)
                        if line.startswith("data:"):
                            data = line[5:].strip()  # 移除 "data: " 前缀
                            if data.strip() == "[DONE]":
                                break

                            try:
                                chunk = json.loads(data)
                                yield chunk
                            except json.JSONDecodeError:
                                logger.warning(f"无法解析JSON数据: {data}")
                                continue
                                
        except httpx.HTTPStatusError as e:
            logger.error(f"LLM流式请求HTTP错误: {e.response.status_code}")
            raise
        except httpx.RequestError as e:
            logger.error(f"LLM流式请求网络错误: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"LLM流式请求未知错误: {str(e)}")
            raise
    
    async def generate_config(
        self,
        prompt: str,
        stream: bool = False,
        **kwargs
    ) -> Dict[str, Any] | AsyncGenerator[Dict[str, Any], None]:
        """生成配置的便捷方法"""
        messages = [
            {
                "role": "user",
                "content": prompt
            }
        ]
        
        if stream:
            return self.chat_completion_stream(messages, **kwargs)
        else:
            return await self.chat_completion(messages, stream=False, **kwargs)


# 全局LLM客户端实例
llm_client = LLMClient()
