"""
设备映射工具
实现{厂商，设备，型号}与知识库ID的映射功能
"""

from typing import Dict, Optional, Tuple
from loguru import logger
from app.config.settings import settings


class DeviceMappingService:
    """设备映射服务类"""
    
    def __init__(self):
        # 设备映射表：{(厂商, 设备, 型号): 知识库ID}
        self.device_mapping = {
            # 华为设备
            ("华为", "路由器", "NE40E"): "kb_huawei_router_ne40e",
            ("华为", "路由器", "NE8000"): "kb_huawei_router_ne8000", 
            ("华为", "路由器", "AR"): "kb_huawei_router_ar",
            ("华为", "交换机", "CE"): "kb_huawei_switch_ce",
            ("华为", "交换机", "S"): "kb_huawei_switch_s",
            ("华为", "防火墙", "USG"): "kb_huawei_firewall_usg",
            
            # 思科设备
            ("思科", "路由器", "ISR"): "kb_cisco_router_isr",
            ("思科", "路由器", "ASR"): "kb_cisco_router_asr",
            ("思科", "交换机", "Catalyst"): "kb_cisco_switch_catalyst",
            ("思科", "防火墙", "ASA"): "kb_cisco_firewall_asa",
            
            # 华三设备
            ("华三", "路由器", "MSR"): "kb_h3c_router_msr",
            ("华三", "交换机", "S"): "kb_h3c_switch_s",
            ("华三", "防火墙", "F"): "kb_h3c_firewall_f",
            
            # 瞻博设备
            ("瞻博", "路由器", "MX"): "kb_juniper_router_mx",
            ("瞻博", "交换机", "EX"): "kb_juniper_switch_ex",
            ("瞻博", "防火墙", "SRX"): "kb_juniper_firewall_srx",
        }
        
        # 厂商别名映射
        self.vendor_aliases = {
            "huawei": "华为",
            "华为技术": "华为",
            "HUAWEI": "华为",
            "cisco": "思科",
            "Cisco": "思科",
            "CISCO": "思科",
            "h3c": "华三",
            "H3C": "华三",
            "华三通信": "华三",
            "juniper": "瞻博",
            "Juniper": "瞻博",
            "JUNIPER": "瞻博",
            "瞻博网络": "瞻博"
        }
        
        # 设备类型别名映射
        self.device_aliases = {
            "router": "路由器",
            "switch": "交换机", 
            "firewall": "防火墙",
            "防火墙设备": "防火墙",
            "路由设备": "路由器",
            "交换设备": "交换机"
        }
        
        # 型号别名映射
        self.model_aliases = {
            # 华为路由器型号
            "ne40": "NE40E",
            "ne8000": "NE8000",
            "ar系列": "AR",
            "ar路由器": "AR",
            
            # 华为交换机型号
            "ce系列": "CE",
            "s系列": "S",
            
            # 思科设备型号
            "isr系列": "ISR",
            "asr系列": "ASR",
            "catalyst系列": "Catalyst",
            "asa系列": "ASA",
            
            # 华三设备型号
            "msr系列": "MSR",
            "s系列交换机": "S",
            "f系列": "F",
            
            # 瞻博设备型号
            "mx系列": "MX",
            "ex系列": "EX", 
            "srx系列": "SRX"
        }
    
    def normalize_vendor(self, vendor: Optional[str]) -> Optional[str]:
        """标准化厂商名称"""
        if not vendor:
            return None
        
        vendor = vendor.strip()
        return self.vendor_aliases.get(vendor, vendor)
    
    def normalize_device(self, device: Optional[str]) -> Optional[str]:
        """标准化设备类型"""
        if not device:
            return None
            
        device = device.strip()
        return self.device_aliases.get(device, device)
    
    def normalize_model(self, model: Optional[str]) -> Optional[str]:
        """标准化型号"""
        if not model:
            return None
            
        model = model.strip()
        return self.model_aliases.get(model, model)
    
    def get_knowledge_base_id(
        self, 
        vendor: Optional[str], 
        device: Optional[str], 
        model: Optional[str]
    ) -> str:
        """获取知识库ID"""
        try:
            # 标准化输入
            normalized_vendor = self.normalize_vendor(vendor)
            normalized_device = self.normalize_device(device)
            normalized_model = self.normalize_model(model)
            
            logger.info(f"设备映射查询: 厂商={normalized_vendor}, 设备={normalized_device}, 型号={normalized_model}")
            
            # 精确匹配
            key = (normalized_vendor, normalized_device, normalized_model)
            if key in self.device_mapping:
                kb_id = self.device_mapping[key]
                logger.info(f"找到精确匹配的知识库ID: {kb_id}")
                return kb_id
            
            # 模糊匹配：厂商+设备类型
            if normalized_vendor and normalized_device:
                for (v, d, m), kb_id in self.device_mapping.items():
                    if v == normalized_vendor and d == normalized_device:
                        logger.info(f"找到厂商+设备类型匹配的知识库ID: {kb_id}")
                        return kb_id
            
            # 模糊匹配：仅厂商
            if normalized_vendor:
                for (v, d, m), kb_id in self.device_mapping.items():
                    if v == normalized_vendor:
                        logger.info(f"找到厂商匹配的知识库ID: {kb_id}")
                        return kb_id
            
            # 返回默认知识库ID
            logger.info(f"未找到匹配的设备映射，使用默认知识库ID: {settings.default_kb_id}")
            return settings.default_kb_id
            
        except Exception as e:
            logger.error(f"获取知识库ID失败: {str(e)}")
            return settings.default_kb_id
    
    def get_supported_devices(self) -> Dict[str, Dict[str, list]]:
        """获取支持的设备列表"""
        supported = {}
        
        for (vendor, device, model), kb_id in self.device_mapping.items():
            if vendor not in supported:
                supported[vendor] = {}
            if device not in supported[vendor]:
                supported[vendor][device] = []
            if model not in supported[vendor][device]:
                supported[vendor][device].append(model)
        
        return supported
    
    def add_device_mapping(
        self, 
        vendor: str, 
        device: str, 
        model: str, 
        kb_id: str
    ) -> bool:
        """添加设备映射"""
        try:
            key = (vendor, device, model)
            self.device_mapping[key] = kb_id
            logger.info(f"添加设备映射成功: {key} -> {kb_id}")
            return True
        except Exception as e:
            logger.error(f"添加设备映射失败: {str(e)}")
            return False


# 全局设备映射服务实例
device_mapping_service = DeviceMappingService()
